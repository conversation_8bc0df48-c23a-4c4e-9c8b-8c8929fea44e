{"name": "remote-mcp-server-authless", "version": "0.0.0", "type": "module", "private": true, "scripts": {"deploy": "wrangler deploy", "dev": "wrangler dev", "format": "biome format --write", "lint:fix": "biome lint --fix", "start": "wrangler dev", "cf-typegen": "wrangler types", "type-check": "tsc --noEmit", "setup-auth": "node scripts/setup-auth.js", "setup-production": "scripts/setup-production.bat", "generate-jwt": "node scripts/generate-jwt.js", "test-auth": "node test/auth.test.js", "test-curl": "curl -H \"X-API-Key: Gv6HoiBHiuvrSDPjnNJPcgj6ldSMU6NK\" -H \"Origin: http://localhost:3000\" http://localhost:8787/health"}, "dependencies": {"@modelcontextprotocol/sdk": "^1.12.1", "agents": "^0.0.95", "zod": "^3.25.61"}, "devDependencies": {"@biomejs/biome": "^1.9.4", "typescript": "^5.8.3", "wrangler": "^4.19.1"}}