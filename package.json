{"name": "remote-mcp-server-authless", "version": "0.0.0", "private": true, "scripts": {"deploy": "wrangler deploy", "dev": "wrangler dev", "format": "biome format --write", "lint:fix": "biome lint --fix", "start": "wrangler dev", "cf-typegen": "wrangler types", "type-check": "tsc --noEmit"}, "dependencies": {"@modelcontextprotocol/sdk": "^1.12.1", "agents": "^0.0.95", "zod": "^3.25.61"}, "devDependencies": {"@biomejs/biome": "^1.9.4", "typescript": "^5.8.3", "wrangler": "^4.19.1"}}