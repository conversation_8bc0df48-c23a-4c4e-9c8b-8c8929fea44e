/**
 * 鉴权功能测试
 * 简单的测试脚本，验证鉴权中间件是否正常工作
 */

import { authenticate, validateApiKey, validateJWT } from '../src/auth.js';
import { generateApiKey, generateJWT } from '../src/auth-utils.js';

// 模拟环境变量
const mockEnv = {
    API_KEYS: 'test-key-1,test-key-2',
    JWT_SECRET: 'test-jwt-secret-12345',
    ALLOWED_ORIGINS: 'http://localhost:3000,https://example.com'
};

// 测试API Key验证
console.log('🧪 测试API Key验证...');

// 测试有效的API Key
const validApiKeyResult = validateApiKey('test-key-1', ['test-key-1', 'test-key-2']);
console.log('✅ 有效API Key测试:', validApiKeyResult.success ? '通过' : '失败');

// 测试无效的API Key
const invalidApiKeyResult = validateApiKey('invalid-key', ['test-key-1', 'test-key-2']);
console.log('✅ 无效API Key测试:', !invalidApiKeyResult.success ? '通过' : '失败');

// 测试JWT验证
console.log('\n🧪 测试JWT验证...');

async function testJWT() {
    try {
        // 生成测试JWT
        const payload = { userId: 'test-user', username: 'testuser' };
        const token = await generateJWT(payload, 'test-jwt-secret-12345', 3600);
        console.log('📝 生成的测试JWT:', token.substring(0, 50) + '...');

        // 验证JWT
        const jwtResult = await validateJWT(token, 'test-jwt-secret-12345');
        console.log('✅ 有效JWT测试:', jwtResult.success ? '通过' : '失败');

        // 测试无效JWT
        const invalidJwtResult = await validateJWT('invalid.jwt.token', 'test-jwt-secret-12345');
        console.log('✅ 无效JWT测试:', !invalidJwtResult.success ? '通过' : '失败');

    } catch (error) {
        console.error('❌ JWT测试失败:', error);
    }
}

// 测试完整的鉴权中间件
async function testAuthMiddleware() {
    console.log('\n🧪 测试鉴权中间件...');

    // 测试API Key请求
    const apiKeyRequest = new Request('http://localhost:8787/sse', {
        headers: { 'X-API-Key': 'test-key-1' }
    });

    const apiKeyAuth = await authenticate(apiKeyRequest, mockEnv);
    console.log('✅ API Key中间件测试:', apiKeyAuth.success ? '通过' : '失败');

    // 测试JWT请求
    const payload = { userId: 'test-user' };
    const token = await generateJWT(payload, 'test-jwt-secret-12345', 3600);
    
    const jwtRequest = new Request('http://localhost:8787/sse', {
        headers: { 'Authorization': `Bearer ${token}` }
    });

    const jwtAuth = await authenticate(jwtRequest, mockEnv);
    console.log('✅ JWT中间件测试:', jwtAuth.success ? '通过' : '失败');

    // 测试无鉴权请求
    const noAuthRequest = new Request('http://localhost:8787/sse');
    const noAuthResult = await authenticate(noAuthRequest, {});
    console.log('✅ 无鉴权配置测试:', noAuthResult.success ? '通过' : '失败');

    // 测试无效请求
    const invalidRequest = new Request('http://localhost:8787/sse', {
        headers: { 'X-API-Key': 'invalid-key' }
    });
    const invalidAuth = await authenticate(invalidRequest, mockEnv);
    console.log('✅ 无效鉴权测试:', !invalidAuth.success ? '通过' : '失败');
}

// 运行所有测试
async function runTests() {
    await testJWT();
    await testAuthMiddleware();
    
    console.log('\n🎉 所有测试完成！');
    console.log('\n💡 提示：');
    console.log('  - 在开发环境中运行: npm run dev');
    console.log('  - 测试健康检查: curl http://localhost:8787/health');
    console.log('  - 测试API Key: curl -H "X-API-Key: test-key-1" http://localhost:8787/sse');
}

runTests().catch(console.error);
