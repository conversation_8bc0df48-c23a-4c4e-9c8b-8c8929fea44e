/**
 * For more details on how to configure Wrangler, refer to:
 * https://developers.cloudflare.com/workers/wrangler/configuration/
 */
{
	"$schema": "node_modules/wrangler/config-schema.json",
	"name": "remote-mcp-server-authless",
	"main": "src/index.ts",
	"compatibility_date": "2025-03-10",
	"compatibility_flags": ["nodejs_compat"],
	"migrations": [
		{
			"new_sqlite_classes": ["MyMCP"],
			"tag": "v1"
		}
	],
	"durable_objects": {
		"bindings": [
			{
				"class_name": "MyMCP",
				"name": "MCP_OBJECT"
			}
		]
	},
	"observability": {
		"enabled": true
	},
	// 环境变量配置示例 - 在生产环境中请使用 wrangler secret 命令设置敏感信息
	"vars": {
		// "API_KEYS": "your-api-key-1,your-api-key-2",
		// "JWT_SECRET": "your-jwt-secret-key",
		// "ALLOWED_ORIGINS": "https://playground.ai.cloudflare.com,http://localhost:3000"
	}
}