#!/usr/bin/env node

/**
 * 鉴权设置脚本
 * 帮助用户快速生成API密钥和JWT密钥
 */

import { generateApiKey, generateJWT, generateSecureRandom } from '../src/auth-utils.js';

console.log('🔐 MCP Server 鉴权设置工具\n');

// 生成API密钥
console.log('📋 生成的API密钥:');
for (let i = 1; i <= 3; i++) {
    const apiKey = generateApiKey(32);
    console.log(`  API Key ${i}: ${apiKey}`);
}

console.log('\n🔑 生成的JWT密钥:');
const jwtSecret = generateSecureRandom(64);
console.log(`  JWT Secret: ${jwtSecret}`);

console.log('\n📝 设置环境变量的命令:');
console.log('  开发环境 (.dev.vars 文件):');
console.log(`    API_KEYS=key1,key2,key3`);
console.log(`    JWT_SECRET=${jwtSecret}`);
console.log(`    ALLOWED_ORIGINS=http://localhost:3000,https://playground.ai.cloudflare.com`);

console.log('\n  生产环境 (Wrangler secrets):');
console.log('    wrangler secret put API_KEYS');
console.log('    wrangler secret put JWT_SECRET');
console.log('    wrangler secret put ALLOWED_ORIGINS');

console.log('\n🧪 测试JWT生成:');
generateJWT({
    userId: 'test-user',
    username: 'testuser',
    roles: ['user']
}, jwtSecret, 3600).then(token => {
    console.log(`  示例JWT Token: ${token}`);
    console.log('\n✅ 设置完成！请参考README.md了解详细配置说明。');
}).catch(err => {
    console.error('❌ JWT生成失败:', err);
});

console.log('\n🔒 安全提醒:');
console.log('  1. 请妥善保管生成的密钥');
console.log('  2. 不要将密钥提交到版本控制系统');
console.log('  3. 定期轮换API密钥');
console.log('  4. 在生产环境中使用HTTPS');
